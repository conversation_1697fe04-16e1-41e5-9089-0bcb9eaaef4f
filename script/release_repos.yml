repositories:
  - name: batteries
    url: https://github.com/leanprover-community/batteries
    toolchain-tag: true
    stable-branch: true
    branch: main
    bump-branch: true
    dependencies: []

  - name: lean4checker
    url: https://github.com/leanprover/lean4checker
    toolchain-tag: true
    stable-branch: true
    branch: master
    dependencies: []

  - name: quote4
    url: https://github.com/leanprover-community/quote4
    toolchain-tag: true
    stable-branch: true
    branch: master
    dependencies: []

  - name: lean4-cli
    url: https://github.com/leanprover/lean4-cli
    toolchain-tag: true
    stable-branch: false
    branch: main
    dependencies: []

  - name: doc-gen4
    url: https://github.com/leanprover/doc-gen4
    toolchain-tag: true
    stable-branch: false
    branch: main
    dependencies: [lean4-cli]

  - name: verso
    url: https://github.com/leanprover/verso
    toolchain-tag: true
    stable-branch: false
    branch: main
    dependencies: []

  - name: reference-manual
    url: https://github.com/leanprover/reference-manual
    toolchain-tag: true
    stable-branch: false
    branch: main
    dependencies: [verso]

  - name: ProofWidgets4
    url: https://github.com/leanprover-community/ProofWidgets4
    toolchain-tag: false
    stable-branch: false
    branch: main
    dependencies:
      - batteries

  - name: aesop
    url: https://github.com/leanprover-community/aesop
    toolchain-tag: true
    stable-branch: true
    branch: master
    dependencies:
      - batteries

  - name: import-graph
    url: https://github.com/leanprover-community/import-graph
    toolchain-tag: true
    stable-branch: false
    branch: main
    dependencies:
      - lean4-cli
      - batteries

  - name: plausible
    url: https://github.com/leanprover-community/plausible
    toolchain-tag: true
    stable-branch: false
    branch: main
    dependencies: []

  - name: mathlib4
    url: https://github.com/leanprover-community/mathlib4
    toolchain-tag: true
    stable-branch: true
    branch: master
    bump-branch: true
    dependencies:
      - aesop
      - ProofWidgets4
      - lean4checker
      - batteries
      - lean4-cli
      - doc-gen4
      - import-graph
      - plausible

  - name: repl
    url: https://github.com/leanprover-community/repl
    toolchain-tag: true
    stable-branch: true
    branch: master
    dependencies:
      - mathlib4
