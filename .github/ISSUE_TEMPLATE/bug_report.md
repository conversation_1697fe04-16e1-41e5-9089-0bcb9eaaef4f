---
name: Bug report
about: Create a bug report
title: ''
labels: bug
assignees: ''

---

### Prerequisites

Please put an X between the brackets as you perform the following steps:

* [ ] Check that your issue is not already filed:
      https://github.com/leanprover/lean4/issues
* [ ] Reduce the issue to a minimal, self-contained, reproducible test case.
      Avoid dependencies to Mathlib or Batteries.
* [ ] Test your test case against the latest nightly release, for example on
      https://live.lean-lang.org/#project=lean-nightly
      (You can also use the settings there to switch to “Lean nightly”)

### Description

[Clear and concise description of the issue]

### Context

[Broader context that the issue occurred in. If there was any prior discussion on [the Lean Zulip](https://leanprover.zulipchat.com), link it here as well.]

### Steps to Reproduce

1.
2.
3.

**Expected behavior:** [Clear and concise description of what you expect to happen]

**Actual behavior:** [Clear and concise description of what actually happens]

### Versions

[Output of `#version` or `#eval Lean.versionString`]
[OS version, if not using live.lean-lang.org.]

### Additional Information

[Additional information, configuration or data that might be necessary to reproduce the issue]

### Impact

Add :+1: to [issues you consider important](https://github.com/leanprover/lean4/issues?q=is%3Aissue+is%3Aopen+sort%3Areactions-%2B1-desc). If others are impacted by this issue, please ask them to add :+1: to it.
