name: Jira sync

on:
  issues:
    types: [closed]

jobs:
  jira-sync:
    runs-on: ubuntu-latest

    steps:
      - name: Move Jira issue to Done
        env:
          JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
          JIRA_USERNAME: ${{ secrets.JIRA_USERNAME }}
          JIRA_BASE_URL: ${{ secrets.JIRA_BASE_URL }}
        run: |
          issue_number=${{ github.event.issue.number }}

          jira_issue_key=$(curl -s -u "${JIRA_USERNAME}:${JIRA_API_TOKEN}" \
            -X GET -H "Content-Type: application/json" \
            "${JIRA_BASE_URL}/rest/api/2/search?jql=summary~\"${issue_number}\"" | \
            jq -r '.issues[0].key')

          if [ -z "$jira_issue_key" ]; then
            exit
          fi

          curl -s -u "${JIRA_USERNAME}:${JIRA_API_TOKEN}" \
            -X POST -H "Content-Type: application/json" \
            --data "{\"transition\": {\"id\": \"41\"}}" \
            "${JIRA_BASE_URL}/rest/api/2/issue/${jira_issue_key}/transitions"

          echo "Moved Jira issue ${jira_issue_key} to Done"
