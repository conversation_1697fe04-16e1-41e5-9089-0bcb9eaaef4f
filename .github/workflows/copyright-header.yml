name: Check for copyright header

on: [pull_request]

jobs:
  check-lean-files:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Verify .lean files start with a copyright header.
      run: |
        FILES=$(find ./src -type d \( -path "./src/lake/examples" -o -path "./src/lake/tests" \) -prune -o -type f -name "*.lean" -exec perl -ne 'BEGIN { $/ = undef; } print "$ARGV\n" if !m{\A/-\nCopyright}; exit;' {} \;)
        if [ -n "$FILES" ]; then
          echo "Found .lean files which do not have a copyright header:"
          echo "$FILES"
          exit 1
        else
          echo "All copyright headers present."
        fi
