/-
Copyright (c) 2025 Lean FRO, LLC. All rights reserved.
Released under Apache 2.0 license as described in the file LICENSE.
Authors: <AUTHORS>
-/
import Grove.Framework

open Grove.Framework Widget

namespace GroveStdlib.Std.LanguageConstructs

namespace RangesAndIterators

end RangesAndIterators

def rangesAndIterators : Node :=
  .section "ranges-and-iterators" "Ranges and iterators" #[]

end GroveStdlib.Std.LanguageConstructs