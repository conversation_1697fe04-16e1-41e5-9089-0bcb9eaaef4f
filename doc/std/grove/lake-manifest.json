{"version": "1.1.0", "packagesDir": ".lake/packages", "packages": [{"url": "https://github.com/TwoFx/grove.git", "type": "git", "subDir": "backend", "scope": "", "rev": "e8127fc6554b99fb988ecdceb770a5e112afbe24", "name": "grove", "manifestFile": "lake-manifest.json", "inputRev": "master", "inherited": false, "configFile": "lakefile.toml"}, {"url": "https://github.com/leanprover/lean4-cli", "type": "git", "subDir": null, "scope": "leanprover", "rev": "1604206fcd0462da9a241beeac0e2df471647435", "name": "<PERSON><PERSON>", "manifestFile": "lake-manifest.json", "inputRev": "main", "inherited": true, "configFile": "lakefile.toml"}], "name": "grovestdlib", "lakeDir": ".lake"}