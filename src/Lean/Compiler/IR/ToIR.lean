/-
Copyright (c) 2024 Lean FRO, LLC. All rights reserved.
Released under Apache 2.0 license as described in the file LICENSE.
Authors: <AUTHORS>
-/
prelude
import Lean.Compiler.LCNF.Basic
import Lean.Compiler.LCNF.CompilerM
import Lean.Compiler.LCNF.PhaseExt
import Lean.Compiler.IR.Basic
import Lean.Compiler.IR.CompilerM
import Lean.Compiler.IR.ToIRType
import Lean.CoreM
import Lean.Environment

namespace Lean.IR

open Lean.Compiler (LCNF.Alt LCNF.Arg LCNF.Code LCNF.Decl LCNF.DeclValue LCNF.LCtx LCNF.LetDecl
                    LCNF.LetValue LCNF.LitValue LCNF.Param LCNF.getMonoDecl?)

namespace ToIR

inductive FVarClassification where
  | var (id : VarId)
  | joinPoint (id : JoinPointId)
  | erased

structure BuilderState where
  fvars : Std.HashMap FVarId FVarClassification := {}
  nextId : Nat := 1

abbrev M := StateRefT BuilderState CoreM

def M.run (x : M α) : CoreM α := do
  x.run' {}

def bindVar (fvarId : FVarId) : M VarId := do
  modifyGet fun s =>
    let varId := { idx := s.nextId }
    ⟨varId, { s with fvars := s.fvars.insertIfNew fvarId (.var varId),
                     nextId := s.nextId + 1 }⟩

def bindVarToVarId (fvarId : FVarId) (varId : VarId) : M Unit := do
  modify fun s => { s with fvars := s.fvars.insertIfNew fvarId (.var varId) }

def newVar : M VarId := do
  modifyGet fun s =>
    let varId := { idx := s.nextId }
    ⟨varId, { s with nextId := s.nextId + 1 }⟩

def bindJoinPoint (fvarId : FVarId) : M JoinPointId := do
  modifyGet fun s =>
    let joinPointId := { idx := s.nextId }
    ⟨joinPointId, { s with fvars := s.fvars.insertIfNew fvarId (.joinPoint joinPointId),
                           nextId := s.nextId + 1 }⟩

def bindErased (fvarId : FVarId) : M Unit := do
  modify fun s => { s with fvars := s.fvars.insertIfNew fvarId .erased }

def findDecl (n : Name) : M (Option Decl) :=
  return findEnvDecl (← Lean.getEnv) n

def addDecl (d : Decl) : M Unit :=
  Lean.modifyEnv fun env => declMapExt.addEntry (env.addExtraName d.name) d

def lowerLitValue (v : LCNF.LitValue) : LitVal :=
  match v with
  | .nat n => .num n
  | .str s => .str s
  | .uint8 v => .num (UInt8.toNat v)
  | .uint16 v => .num (UInt16.toNat v)
  | .uint32 v => .num (UInt32.toNat v)
  | .uint64 v | .usize v => .num (UInt64.toNat v)

def lowerArg (a : LCNF.Arg) : M Arg := do
  match a with
  | .fvar fvarId =>
    match (← get).fvars[fvarId]? with
    | some (.var varId) => return .var varId
    | some .erased => return .irrelevant
    | some (.joinPoint ..) | none => panic! "unexpected value"
  | .erased | .type .. => return .irrelevant

inductive TranslatedProj where
  | expr (e : Expr)
  | erased
  deriving Inhabited

def lowerProj (base : VarId) (ctorInfo : CtorInfo) (field : CtorFieldInfo)
    : TranslatedProj × IRType :=
  match field with
  | .object i => ⟨.expr (.proj i base), .object⟩
  | .usize i => ⟨.expr (.uproj i base), .usize⟩
  | .scalar _ offset irType => ⟨.expr (.sproj (ctorInfo.size + ctorInfo.usize) offset base), irType⟩
  | .irrelevant => ⟨.erased, .irrelevant⟩

def lowerParam (p : LCNF.Param) : M Param := do
  let x ← bindVar p.fvarId
  let ty ← toIRType p.type
  return { x, borrow := p.borrow, ty }

mutual
partial def lowerCode (c : LCNF.Code) : M FnBody := do
  match c with
  | .let decl k => lowerLet decl k
  | .jp decl k =>
    let joinPoint ← bindJoinPoint decl.fvarId
    let params ← decl.params.mapM lowerParam
    let body ← lowerCode decl.value
    return .jdecl joinPoint params body (← lowerCode k)
  | .jmp fvarId args =>
    match (← get).fvars[fvarId]? with
    | some (.joinPoint joinPointId) =>
      return .jmp joinPointId (← args.mapM lowerArg)
    | some (.var ..) | some .erased | none => panic! "unexpected value"
  | .cases cases =>
    match (← get).fvars[cases.discr]? with
    | some (.var varId) =>
      return .case cases.typeName
                   varId
                   (← toIRType cases.resultType)
                   (← cases.alts.mapM (lowerAlt varId))
    | some (.joinPoint ..) | some .erased | none => panic! "unexpected value"
  | .return fvarId =>
    let arg := match (← get).fvars[fvarId]? with
    | some (.var varId) => .var varId
    | some .erased => .irrelevant
    | some (.joinPoint ..) | none => panic! "unexpected value"
    return .ret arg
  | .unreach .. => return .unreachable
  | .fun .. => panic! "all local functions should be λ-lifted"

partial def lowerLet (decl : LCNF.LetDecl) (k : LCNF.Code) : M FnBody := do
  match decl.value with
  | .lit litValue =>
    mkExpr (.lit (lowerLitValue litValue))
  | .proj typeName i fvarId =>
    match (← get).fvars[fvarId]? with
    | some (.var varId) =>
      let some (.inductInfo { ctors := [ctorName], .. }) := (← Lean.getEnv).find? typeName
        | panic! "projection of non-structure type"
      let ⟨ctorInfo, fields⟩ ← getCtorLayout ctorName
      let ⟨result, type⟩ := lowerProj varId ctorInfo fields[i]!
      match result with
      | .expr e =>
        let var ← bindVar decl.fvarId
        return .vdecl var type e (← lowerCode k)
      | .erased =>
        bindErased decl.fvarId
        lowerCode k
    | some .erased =>
      bindErased decl.fvarId
      lowerCode k
    | some (.joinPoint ..) | none => panic! "unexpected value"
  | .const ``Nat.succ _ args =>
    let irArgs ← args.mapM lowerArg
    let var ← bindVar decl.fvarId
    let tmpVar ← newVar
    let k := (.vdecl var .object (.fap ``Nat.add #[irArgs[0]!, (.var tmpVar)]) (← lowerCode k))
    return .vdecl tmpVar .object (.lit (.num 1)) k
  | .const name _ args =>
    let irArgs ← args.mapM lowerArg
    if let some code ← tryIrDecl? name irArgs then
      return code
    let env ← Lean.getEnv
    match env.find? name with
    | some (.ctorInfo ctorVal) =>
      if isExtern env name then
        return (← mkExpr (.fap name irArgs))

      let type ← nameToIRType ctorVal.induct
      if type.isScalar then
        let var ← bindVar decl.fvarId
        return .vdecl var type (.lit (.num ctorVal.cidx)) (← lowerCode k)

      assert! type == .object
      let ⟨ctorInfo, fields⟩ ← getCtorLayout name
      let args := args.extract (start := ctorVal.numParams)
      let objArgs : Array Arg ← do
        let mut result : Array Arg := #[]
        for i in *...fields.size do
          match args[i]! with
          | .fvar fvarId =>
            if let some (.var varId) := (← get).fvars[fvarId]? then
              if fields[i]! matches .object .. then
                result := result.push (.var varId)
          | .type _ | .erased =>
            if fields[i]! matches .object .. then
              result := result.push .irrelevant
        pure result
      let objVar ← bindVar decl.fvarId
      let rec lowerNonObjectFields (_ : Unit) : M FnBody :=
        let rec loop (usizeCount : Nat) (i : Nat) : M FnBody := do
          match args[i]? with
          | some (.fvar fvarId) =>
            match (← get).fvars[fvarId]? with
            | some (.var varId) =>
              match fields[i]! with
              | .usize .. =>
                let k ← loop (usizeCount + 1) (i + 1)
                return .uset objVar (ctorInfo.size + usizeCount) varId k
              | .scalar _ offset argType =>
                let k ← loop usizeCount (i + 1)
                return .sset objVar (ctorInfo.size + ctorInfo.usize) offset varId argType k
              | .object .. | .irrelevant => loop usizeCount (i + 1)
            | _ => loop usizeCount (i + 1)
          | some (.type _) | some .erased => loop usizeCount (i + 1)
          | none => lowerCode k
        loop 0 0
      return .vdecl objVar type (.ctor ctorInfo objArgs) (← lowerNonObjectFields ())
    | some (.defnInfo ..) | some (.opaqueInfo ..) =>
      mkExpr (.fap name irArgs)
    | some (.axiomInfo ..) | .some (.quotInfo ..) | .some (.inductInfo ..) | .some (.thmInfo ..) =>
      throwNamedError lean.dependsOnNoncomputable f!"'{name}' not supported by code generator; consider marking definition as 'noncomputable'"
    | some (.recInfo ..) =>
      throwError f!"code generator does not support recursor '{name}' yet, consider using 'match ... with' and/or structural recursion"
    | none => panic! "reference to unbound name"
  | .fvar fvarId args =>
    match (← get).fvars[fvarId]? with
    | some (.var id) =>
      let irArgs ← args.mapM lowerArg
      mkExpr (.ap id irArgs)
    | some .erased => mkErased ()
    | some (.joinPoint ..) | none => panic! "unexpected value"
  | .erased => mkErased ()
where
  mkVar (v : VarId) : M FnBody := do
    bindVarToVarId decl.fvarId v
    lowerCode k

  mkExpr (e : Expr) : M FnBody := do
    let var ← bindVar decl.fvarId
    let type ← match e with
    | .ctor .. | .pap .. | .ap .. | .proj .. => pure <| .object
    | _ => toIRType decl.type
    return .vdecl var type e (← lowerCode k)

  mkErased (_ : Unit) : M FnBody := do
    bindErased decl.fvarId
    lowerCode k

  mkPartialApp (e : Expr) (restArgs : Array Arg) : M FnBody := do
    let var ← bindVar decl.fvarId
    let tmpVar ← newVar
    return .vdecl tmpVar .object e (.vdecl var .object (.ap tmpVar restArgs) (← lowerCode k))

  tryIrDecl? (name : Name) (args : Array Arg) : M (Option FnBody) := do
    if let some decl ← LCNF.getMonoDecl? name then
      let numArgs := args.size
      let numParams := decl.params.size
      if numArgs < numParams then
        return some (← mkExpr (.pap name args))
      else if numArgs == numParams then
        return some (← mkExpr (.fap name args))
      else
        let firstArgs := args.extract 0 numParams
        let restArgs := args.extract numParams numArgs
        return some (← mkPartialApp (.fap name firstArgs) restArgs)
    else
      return none

partial def lowerAlt (discr : VarId) (a : LCNF.Alt) : M Alt := do
  match a with
  | .alt ctorName params code =>
    let ⟨ctorInfo, fields⟩ ← getCtorLayout ctorName
    let lowerParams (params : Array LCNF.Param) (fields : Array CtorFieldInfo) : M FnBody := do
      let rec loop (i : Nat) : M FnBody := do
        match params[i]?, fields[i]? with
        | some param, some field =>
          let ⟨result, type⟩ := lowerProj discr ctorInfo field
          match result with
          | .expr e =>
            return .vdecl (← bindVar param.fvarId)
                          type
                          e
                          (← loop (i + 1))
          | .erased =>
            bindErased param.fvarId
            loop (i + 1)
        | none, none => lowerCode code
        | _, _ => panic! "mismatched fields and params"
      loop 0
    let body ← lowerParams params fields
    return .ctor ctorInfo body
  | .default code =>
    return .default (← lowerCode code)
end

def lowerResultType (type : Lean.Expr) (arity : Nat) : M IRType :=
  toIRType (resultTypeForArity type arity)
where resultTypeForArity (type : Lean.Expr) (arity : Nat) : Lean.Expr :=
  if arity == 0 then
    type
  else
    match type with
    | .forallE _ _ b _ => resultTypeForArity b (arity - 1)
    | .const ``lcErased _ => mkConst ``lcErased
    | _ => panic! "invalid arity"

def lowerDecl (d : LCNF.Decl) : M (Option Decl) := do
  let params ← d.params.mapM lowerParam
  let resultType ← lowerResultType d.type d.params.size
  match d.value with
  | .code code =>
    let body ← lowerCode code
    pure <| some <| .fdecl d.name params resultType body {}
  | .extern externAttrData =>
    if externAttrData.entries.isEmpty then
      -- TODO: This matches the behavior of the old compiler, but we should
      -- find a better way to handle this.
      addDecl (mkDummyExternDecl d.name params resultType)
      pure <| none
    else
      pure <| some <| .extern d.name params resultType externAttrData

end ToIR

def toIR (decls: Array LCNF.Decl) : CoreM (Array Decl) := do
  let mut irDecls := #[]
  for decl in decls do
    if let some irDecl ← ToIR.lowerDecl decl |>.run then
      irDecls := irDecls.push irDecl
  return irDecls

end Lean.IR
