/-
Copyright (c) 2024 Lean FRO. All rights reserved.
Released under Apache 2.0 license as described in the file LICENSE.
Authors: <AUTHORS>
module

prelude
public import Init.Data.Vector.Basic
public import Init.Data.Vector.Lemmas
public import Init.Data.Vector.Lex
public import Init.Data.Vector.MapIdx
public import Init.Data.Vector.Count
public import Init.Data.Vector.DecidableEq
public import Init.Data.Vector.Zip
public import Init.Data.Vector.OfFn
public import Init.Data.Vector.Range
public import Init.Data.Vector.Erase
public import Init.Data.Vector.Monadic
public import Init.Data.Vector.InsertIdx
public import Init.Data.Vector.FinRange
public import Init.Data.Vector.Extract
public import Init.Data.Vector.Perm
public import Init.Data.Vector.Find

public section
