/-
Copyright (c) 2017 Microsoft Corporation. All rights reserved.
Released under Apache 2.0 license as described in the file LICENSE.
Authors: <AUTHORS>
-/
module

prelude
public import Init.Data.Array.Basic
public import Init.Data.Array.QSort
public import Init.Data.Array.BinSearch
public import Init.Data.Array.InsertionSort
public import Init.Data.Array.DecidableEq
public import Init.Data.Array.Mem
public import Init.Data.Array.Attach
public import Init.Data.Array.BasicAux
public import Init.Data.Array.Lemmas
public import Init.Data.Array.TakeDrop
public import Init.Data.Array.Bootstrap
public import Init.Data.Array.GetLit
public import Init.Data.Array.MapIdx
public import Init.Data.Array.Set
public import Init.Data.Array.Monadic
public import Init.Data.Array.FinRange
public import Init.Data.Array.Perm
public import Init.Data.Array.Find
public import Init.Data.Array.Lex
public import Init.Data.Array.Range
public import Init.Data.Array.Erase
public import Init.Data.Array.Zip
public import Init.Data.Array.InsertIdx
public import Init.Data.Array.Extract

public section
